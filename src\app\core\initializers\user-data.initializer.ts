import { APP_INITIALIZER } from '@angular/core';
import { UserDataService } from '../services/user-data/user-data.service';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

export function initializeUserData(userDataService: UserDataService) {
  return (): Observable<any> => {
    console.log('Initializing user data...');
    
    return userDataService.initializeUserData().pipe(
      tap((completeUser) => {
        if (completeUser) {
          console.log('User data initialized successfully:', completeUser.name);
        } else {
          console.log('No authenticated user found');
        }
      }),
      catchError((error) => {
        console.warn('Failed to initialize user data:', error);
        // Don't fail app initialization if user data loading fails
        return of(null);
      })
    );
  };
}

export const USER_DATA_INITIALIZER = {
  provide: APP_INITIALIZER,
  useFactory: initializeUserData,
  deps: [UserDataService],
  multi: true
};
