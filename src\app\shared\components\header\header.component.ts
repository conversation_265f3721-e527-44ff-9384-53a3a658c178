import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth/auth.service';
import { SubdomainService } from 'src/app/core/services/subdomain/subdomain.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css'],
})
export class HeaderComponent {
  @Input() isPublic: boolean = true;
  isLoggedIn: boolean = false;
  isMenuOpen: boolean = false;
  isMobileMenuOpen = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private subdomainService: SubdomainService
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe((user) => {
      this.isLoggedIn = !!user;
    });
  }

  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
  }
  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  logout(): void {
    console.log('HeaderComponent: Logging out user');

    // If on subdomain, use cross-domain logout to clear tokens from both domains
    if (this.subdomainService.areSubdomainsEnabled()) {
      const currentSubdomain = this.subdomainService.getCurrentSubdomain();
      if (currentSubdomain) {
        console.log(
          'HeaderComponent: On subdomain, initiating cross-domain logout'
        );
        // Use the new cross-domain logout method
        this.authService.logoutFromSubdomain();
        return;
      }
    }

    // Fallback for main domain logout
    console.log('HeaderComponent: On main domain, using regular logout');
    this.authService.logout();
    this.router.navigate(['/auth/login']);
  }
}
